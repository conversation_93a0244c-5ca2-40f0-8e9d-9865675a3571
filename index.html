<!DOCTYPE html>
<html lang="zh">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>友商营业额测算 - Ella</title>
  <script src="https://cdn.jsdelivr.net/npm/xlsx@0.18.5/dist/xlsx.full.min.js"></script>
  <script src="https://cdn.tailwindcss.com"></script>
  
  <!-- 添加自定义样式 -->
  <style>
    .text-shadow {
      text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.2);
    }
  </style>
  
  <!-- 添加excel处理脚本 -->
  <script src="js/excel-handler.js"></script>
  <!-- 添加日期时间选择器脚本 -->
  <script src="js/datetime-picker.js"></script>
  <!-- 添加时段系数脚本 -->
  <script src="js/time-period.js"></script>
  <!-- 添加周系数脚本 -->
  <script src="js/week-coefficient.js"></script>
  <!-- 添加月系数脚本 -->
  <script src="js/month-coefficient.js"></script>
  <!-- 添加经营系数脚本 -->
  <script src="js/business-coefficient.js"></script>
  <!-- 添加测算结果脚本 -->
  <script src="js/calculation-result.js"></script>
</head>
<body class="bg-gray-100 p-3 md:p-4">
  <div class="max-w-6xl mx-auto bg-white rounded-xl shadow p-4">
    <div class="flex flex-col md:flex-row justify-between items-center mb-4">
      <h1 class="text-xl md:text-2xl font-bold">友商营业额测算 - Ella v0.3.1</h1>
      
      <!-- 上传 Excel 和测算按钮 -->
      <div class="w-full md:w-auto mt-3 md:mt-0 flex flex-col md:flex-row items-center gap-2">
        <div class="flex items-center w-full md:w-auto">
          <label class="whitespace-nowrap font-medium mr-2">上传配置文件:</label>
          <input type="file" id="excelFile" accept=".xlsx" class="border p-1 rounded text-sm w-48">
        </div>
        <button id="calculateButton" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-1.5 rounded font-medium">开始测算</button>
      </div>
    </div>

    <!-- 输入区域 -->
    <div class="mb-4">
      <!-- 基础信息模块 - 隐藏 -->
      <div class="border rounded-lg p-3 mb-4" style="display: none;">
        <h3 class="font-bold text-gray-800 mb-2 border-b pb-1 flex items-center text-shadow">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
          </svg>
          基础信息
        </h3>
        <div class="grid grid-cols-2 gap-3">
          <select id="province" class="border p-1.5 rounded text-sm"><option>省份</option></select>
          <select id="city" class="border p-1.5 rounded text-sm"><option>城市</option></select>
          <select id="district" class="border p-1.5 rounded text-sm"><option>区县</option></select>
          <div class="border p-1.5 rounded bg-gray-50 flex items-center text-sm">
            <span class="text-gray-600 mr-2">城市等级:</span>
            <span id="cityLevel" class="font-medium">--</span>
          </div>
        </div>
      </div>

      <!-- 目标信息和输入信息区域 - 与测算结果宽度一致 -->
      <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
        <!-- 目标信息模块 -->
        <div class="border rounded-lg p-3">
          <h3 class="font-bold text-gray-800 mb-2 border-b pb-1 flex items-center text-shadow">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
            </svg>
            目标信息
          </h3>
          <div class="grid grid-cols-2 gap-3">
            <select id="brand" class="border p-1.5 rounded text-sm"><option>品牌</option></select>
            <select id="areaType" class="border p-1.5 rounded text-sm"><option>商圈类型</option></select>
            <select id="orderChannel" class="border p-1.5 rounded text-sm"><option>订单渠道</option></select>
          </div>
        </div>

        <!-- 输入信息模块 -->
        <div class="border rounded-lg p-3">
          <h3 class="font-bold text-gray-800 mb-2 border-b pb-1 flex items-center text-shadow">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
            输入信息
          </h3>
          <div class="grid grid-cols-2 gap-3">
            <!-- 日期时间选择按钮 -->
            <button id="timeButton" class="border p-1.5 rounded bg-white hover:bg-gray-50 text-left text-sm col-span-2 mb-2">选择日期时间</button>

            <input type="number" id="mobileOrders" placeholder="手机点餐单号" class="border p-1.5 rounded text-sm">
            <input type="number" id="otherOrders" placeholder="其他点餐单号" class="border p-1.5 rounded text-sm">
            <input type="number" id="meituanOrders" placeholder="美团单量" class="border p-1.5 rounded text-sm">
            <input type="number" id="elemeOrders" placeholder="饿了么单量" class="border p-1.5 rounded text-sm">
          </div>
        </div>
      </div>

      <!-- 时间系数模块 -->
      <div class="border rounded-lg p-3 mb-4" style="display: none;">
        <h3 class="font-bold text-gray-800 mb-2 border-b pb-1 flex items-center text-shadow">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          时间系数
        </h3>
        <div class="grid grid-cols-2 gap-3">
          <div class="border p-1.5 rounded bg-gray-50 flex items-center text-sm">
            <span class="text-gray-600 mr-1">当前时段:</span>
            <span id="currentTimePeriod" class="font-medium">--</span>
          </div>

          <div class="border p-1.5 rounded bg-gray-50 flex items-center text-sm">
            <span class="text-gray-600 mr-1">时段系数:</span>
            <span id="timePeriodCoefficient" class="font-medium">--</span>
          </div>

          <div class="border p-1.5 rounded bg-gray-50 flex items-center text-sm">
            <span class="text-gray-600 mr-1">周系数:</span>
            <span id="weekCoefficient" class="font-medium">--</span>
          </div>

          <div class="border p-1.5 rounded bg-gray-50 flex items-center text-sm">
            <span class="text-gray-600 mr-1">月系数:</span>
            <span id="monthCoefficient" class="font-medium">--</span>
          </div>

          <div class="border p-1.5 rounded bg-gray-50 flex items-center text-sm">
            <span class="text-gray-600 mr-1">当前月份:</span>
            <span id="currentMonth" class="font-medium">--</span>
          </div>

          <div class="border p-1.5 rounded bg-gray-50 flex items-center text-sm">
            <span class="text-gray-600 mr-1">月天数:</span>
            <span id="daysInMonth" class="font-medium">--</span>
          </div>
        </div>
      </div>

      <!-- 经营系数区域 -->
      <div class="border rounded-lg p-3 mb-4" style="display: none;">
        <h3 class="font-bold text-gray-800 mb-2 border-b pb-1 flex items-center text-shadow">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z" />
          </svg>
          经营系数
        </h3>
        <div class="grid grid-cols-2 gap-3">
          <div class="border p-1.5 rounded bg-gray-50 flex items-center text-sm">
            <span class="text-gray-600 mr-1">堂食客单价:</span>
            <span id="dineUnitPrice" class="font-medium">--</span>
            <span class="text-gray-600 ml-1">元</span>
          </div>

          <div class="border p-1.5 rounded bg-gray-50 flex items-center text-sm">
            <span class="text-gray-600 mr-1">外送客单价:</span>
            <span id="deliveryUnitPrice" class="font-medium">--</span>
            <span class="text-gray-600 ml-1">元</span>
          </div>

          <div class="border p-1.5 rounded bg-gray-50 flex items-center text-sm">
            <span class="text-gray-600 mr-1">第三方外送占比参考:</span>
            <span id="thirdPartyRatio" class="font-medium">--</span>
          </div>

          <div class="border p-1.5 rounded bg-gray-50 flex items-center text-sm">
            <span class="text-gray-600 mr-1">线上点餐占比参考:</span>
            <span id="onlineOrderRatio" class="font-medium">--</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 输出区域 -->
    <div class="bg-gray-50 p-4 rounded-lg shadow-inner border">
      <h2 class="text-lg font-bold text-gray-800 mb-3 flex items-center text-shadow">
        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
        </svg>
        测算结果
      </h2>
      <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div class="bg-white p-3 rounded border shadow-sm hover:shadow-md transition-shadow duration-300">
          <h3 class="font-bold text-gray-800 mb-2 pb-1 border-b flex items-center text-shadow">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 3h2l.4 2M7 13h10l4-8H5.4M7 13L5.4 5M7 13l-2.293 2.293c-.63.63-.184 1.707.707 1.707H17m0 0a2 2 0 100 4 2 2 0 000-4zm-8 2a2 2 0 11-4 0 2 2 0 014 0z" />
            </svg>
            堂食预估
          </h3>
          <div class="space-y-2">
            <div class="flex justify-between items-center py-1 px-2 rounded bg-gray-50">
              <span class="text-gray-600">堂食月营业额预估:</span>
              <div class="flex items-center">
                <span id="dineRevenue" class="font-medium text-green-700">--</span>
                <span class="text-gray-600 ml-1">元</span>
              </div>
            </div>
          </div>
        </div>
        <div class="bg-white p-3 rounded border shadow-sm hover:shadow-md transition-shadow duration-300">
          <h3 class="font-bold text-gray-800 mb-2 pb-1 border-b flex items-center text-shadow">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
            </svg>
            外送预估
          </h3>
          <div class="space-y-2">
            <div class="flex justify-between items-center py-1 px-2 rounded bg-gray-50">
              <span class="text-gray-600">外送月营业额预估:</span>
              <div class="flex items-center">
                <span id="deliveryRevenue" class="font-medium text-blue-700">--</span>
                <span class="text-gray-600 ml-1">元</span>
              </div>
            </div>
            <div class="flex justify-between items-center py-1 px-2 rounded bg-gray-50" style="display: none;">
              <span class="text-gray-600">外送年度占比参考值:</span>
              <span id="deliveryYearRatio" class="font-medium text-blue-700">--</span>
            </div>

          </div>
        </div>
      </div>
      <div class="mt-4 pt-3 border-t border-gray-300">
        <div class="bg-blue-50 p-3 rounded-lg border border-blue-200 flex justify-between items-center">
          <span class="font-bold text-gray-800 text-shadow">月总营业额预估:</span> 
          <div class="flex items-center">
            <span id="totalRevenue" class="font-bold text-lg text-red-600">--</span> 
            <span class="text-gray-600 ml-1">元</span>
          </div>
        </div>
      </div>

    </div>
  </div>

  <script>
    // 全局变量，用于存储Excel数据
    let excelData = [];

    // 为Excel文件上传添加事件监听器
    document.getElementById('excelFile').addEventListener('change', handleExcelUpload);
    
    // 初始化日期时间选择器、时段系数和周系数
    document.addEventListener('DOMContentLoaded', function() {
      try {
        // 按顺序初始化各个功能
        initDateTimePicker();
        
        // 确保函数存在再调用
        if (typeof initTimePeriod === 'function') {
          initTimePeriod();
        }
        
        if (typeof initWeekCoefficient === 'function') {
          initWeekCoefficient();
        }
        
        if (typeof initMonthCoefficient === 'function') {
          initMonthCoefficient();
        }
        
        if (typeof initBusinessCoefficient === 'function') {
          initBusinessCoefficient();
        }
        
        if (typeof initCalculationResult === 'function') {
          initCalculationResult();
        }
      } catch (error) {
        console.error('初始化时出错:', error);
      }
    });
  </script>
</body>
</html>
